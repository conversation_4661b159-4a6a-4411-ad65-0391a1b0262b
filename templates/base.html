<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">

    <title>{% block title %}首页{% endblock %}</title>
    <meta content="" name="description">
    <meta content="" name="keywords">

    <!-- Favicons -->
    <link href="/static/img/favicon.png" rel="icon">
    <link href="/static/img/apple-touch-icon.png" rel="apple-touch-icon">

    <!-- Vendor CSS Files -->
    <link href="/static/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
    <link href="/static/vendor/quill/quill.snow.css" rel="stylesheet">
    <link href="/static/vendor/quill/quill.bubble.css" rel="stylesheet">
    <link href="/static/vendor/remixicon/remixicon.css" rel="stylesheet">
    <link href="/static/vendor/simple-datatables/style.css" rel="stylesheet">

    <!-- Template Main CSS File -->
    <link href="/static/css/style.css" rel="stylesheet">

</head>

<style>
    .sidebar {
        width: 250px;
    }

    @media (min-width: 1200px) {
        #main, #footer {
            margin-left: 260px;
        }
    }
</style>

<body>

<header id="header" class="header fixed-top d-flex align-items-center">

    <div class="d-flex align-items-center justify-content-between">
        <a href="{% url 'home' %}" class="logo d-flex align-items-center">
            <img src="/static/img/logo.png" alt="">
            <span class="d-none d-lg-block" style="font-size: 22px">Django万能模板</span>
        </a>
        <i class="bi bi-list toggle-sidebar-btn"></i>
    </div><!-- End Logo -->

    <nav class="header-nav ms-auto">
        <ul class="d-flex align-items-center">

            <li class="nav-item dropdown pe-3">
                <a class="nav-link nav-profile d-flex align-items-center pe-0" href="#" data-bs-toggle="dropdown">
                    <img src="/media/{{ userInfo.avatar }}" alt="Profile" class="rounded-circle">
                    <span class="d-none d-md-block dropdown-toggle ps-2">{{ userInfo.username }}</span>
                </a><!-- End Profile Image Icon -->

                <ul class="dropdown-menu dropdown-menu-end dropdown-menu-arrow profile">
                    <li class="dropdown-header">
                        <h6>{{ userInfo.username }}</h6>
                        <span>{{ userInfo.textarea }}</span>
                    </li>
                    <li>
                        <hr class="dropdown-divider">
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center" href="{% url 'logOut' %}">
                            <i class="bi bi-box-arrow-right"></i>
                            <span>退出登录</span>
                        </a>
                    </li>
                </ul><!-- End Profile Dropdown Items -->
            </li><!-- End Profile Nav -->

        </ul>
    </nav><!-- End Icons Navigation -->

</header><!-- End Header -->

{% block sidebar %}
    {% with active_menu=active_menu|default:block.super %}
        <aside id="sidebar" class="sidebar">
            <ul class="sidebar-nav" id="sidebar-nav">
                <li class="nav-item">
                    <a class="nav-link {% if active_menu == 'home' %}{% else %}collapsed{% endif %}"
                       href="{% url 'home' %}">
                        <i class="bi bi-grid"></i>
                        <span>首页</span>
                    </a>
                </li>
                <li class="nav-heading">个人信息</li>
                <li class="nav-item">
                    <a class="nav-link {% if active_menu == 'changeSelfInfo' %}{% else %}collapsed{% endif %}"
                       href="{% url 'changeSelfInfo' %}">
                        <i class="bi bi-menu-button-wide"></i><span>修改信息</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if active_menu == 'changePassword' %}{% else %}collapsed{% endif %}"
                       href="{% url 'changePassword' %}">
                        <i class="bi bi-journal-text"></i><span>修改密码</span>
                    </a>
                </li>

                <li class="nav-heading">系统管理</li>
                <li class="nav-item">
                    <a class="nav-link collapsed" href="http://127.0.0.1:8000/admin/" target="_blank">
                        <i class="bi bi-gear"></i><span>后台管理</span>
                    </a>
                </li>
            </ul>
        </aside><!-- End Sidebar-->
    {% endwith %}
{% endblock %}

{% block active_menu %}{% endblock %}

<main id="main" class="main">
    {% block content %}{% endblock %}
</main><!-- End #main -->

<a href="#" class="back-to-top d-flex align-items-center justify-content-center"><i
        class="bi bi-arrow-up-short"></i></a>

<!-- Vendor JS Files -->
<script src="/static/vendor/apexcharts/apexcharts.min.js"></script>
<script src="/static/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="/static/vendor/chart.js/chart.min.js"></script>
<script src="/static/vendor/echarts/echarts.min.js"></script>
<script src="/static/vendor/quill/quill.min.js"></script>
<script src="/static/vendor/simple-datatables/simple-datatables.js"></script>
<script src="/static/vendor/tinymce/tinymce.min.js"></script>
<script src="/static/vendor/php-email-form/validate.js"></script>

<!-- Template Main JS File -->
<script src="/static/js/main.js"></script>

{% block echarts %}{% endblock %}

</body>

</html>