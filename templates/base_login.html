<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">

    <title>{% block title %}登录{% endblock %}</title>
    <meta content="" name="description">
    <meta content="" name="keywords">

    <!-- Favicons -->
    <link href="/static/img/favicon.png" rel="icon">
    <link href="/static/img/apple-touch-icon.png" rel="apple-touch-icon">

    <!-- Google Fonts -->
    <link href="https://fonts.gstatic.com" rel="preconnect">
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Nunito:300,300i,400,400i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i"
          rel="stylesheet">

    <!-- Vendor CSS Files -->
    <link href="/static/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
    <link href="/static/vendor/quill/quill.snow.css" rel="stylesheet">
    <link href="/static/vendor/quill/quill.bubble.css" rel="stylesheet">
    <link href="/static/vendor/remixicon/remixicon.css" rel="stylesheet">
    <link href="/static/vendor/simple-datatables/style.css" rel="stylesheet">

    <!-- Template Main CSS File -->
    <link href="/static/css/style.css" rel="stylesheet">

</head>

<style>
    main {
        background: url(/static/img/背景.jpg);
        background-repeat: no-repeat;
        background-size: cover
    }
</style>

<body>

<main>
    <div class="container">

        <section class="section register min-vh-100 d-flex flex-column align-items-center justify-content-center py-4">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-4 col-md-6 d-flex flex-column align-items-center justify-content-center">

                        <div class="d-flex justify-content-center py-4">
                            <a href="#" class="logo d-flex align-items-center w-auto">
                                <img src="/static/img/logo.png" alt="">
                                <span class="d-none d-lg-block">登录标题</span>
                            </a>
                        </div><!-- End Logo -->

                        {% block content %}
                            <div class="card mb-3">
                                <div class="card-body">

                                    <div class="pt-4 pb-2">
                                        <h5 class="card-title text-center pb-0 fs-4">登录您的用户</h5>
                                        <p class="text-center small">输入用户名和密码</p>
                                    </div>

                                    <form method="post" action="{% url 'login' %}" class="row g-3 needs-validation"
                                          novalidate>

                                        <div class="col-12">
                                            <label for="yourUsername" class="form-label">用户名</label>
                                            <div class="input-group has-validation">
                                                {#                                            <span class="input-group-text" id="inputGroupPrepend">@</span>#}
                                                <input type="text" name="username" class="form-control"
                                                       id="yourUsername"
                                                       required>
                                                <div class="invalid-feedback">请输入您的用户名</div>
                                            </div>
                                        </div>

                                        <div class="col-12">
                                            <label for="yourPassword" class="form-label">密码</label>
                                            <input type="password" name="password" class="form-control"
                                                   id="yourPassword"
                                                   required>
                                            <div class="invalid-feedback">请输入您的密码</div>
                                        </div>

                                        <div class="col-12">
                                            {#                                        <div class="form-check">#}
                                            {#                                            <input class="form-check-input" type="checkbox" name="remember" value="true"#}
                                            {#                                                   id="rememberMe">#}
                                            {#                                            <label class="form-check-label" for="rememberMe">记住我</label>#}
                                            {#                                        </div>#}
                                        </div>
                                        <div class="col-12">
                                            <button class="btn btn-primary w-100" type="submit">登录</button>
                                        </div>
                                        <div class="col-12">
                                            {#                                            <p class="small mb-0">还没有账号? <a href="/app/register">创建账号</a>#}
                                            <p class="small mb-0">还没有账号? <a
                                                    href="{% url 'register' %}">创建账号</a>
                                            </p>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        {% endblock %}

                    </div>

                </div>
            </div>
 

        </section>

    </div>
</main><!-- End #main -->

<a href="#" class="back-to-top d-flex align-items-center justify-content-center"><i
        class="bi bi-arrow-up-short"></i></a>

<!-- Vendor JS Files -->
<script src="/static/vendor/apexcharts/apexcharts.min.js"></script>
<script src="/static/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="/static/vendor/chart.js/chart.min.js"></script>
<script src="/static/vendor/echarts/echarts.min.js"></script>
<script src="/static/vendor/quill/quill.min.js"></script>
<script src="/static/vendor/simple-datatables/simple-datatables.js"></script>
<script src="/static/vendor/tinymce/tinymce.min.js"></script>
<script src="/static/vendor/php-email-form/validate.js"></script>

<!-- Template Main JS File -->
<script src="/static/js/main.js"></script>

</body>

</html>