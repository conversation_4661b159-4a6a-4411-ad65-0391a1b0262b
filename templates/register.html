{% extends 'base_login.html' %}


{% block title %}
    注册
{% endblock %}

{% block content %}
    <div class="card mb-3">

        <div class="card-body">

            <div class="pt-4 pb-2">
                <h5 class="card-title text-center pb-0 fs-4">注册用户</h5>
                <p class="text-center small">请输入您的用户名和密码</p>
            </div>

            {#            <form method="post" action="/app/register/" class="row g-3 needs-validation" novalidate>#}
            <form method="post" action="{% url 'register' %}" class="row g-3 needs-validation" novalidate>
                <div class="col-12">
                    <label for="yourName" class="form-label">用户名</label>
                    <input type="text" name="username" class="form-control" id="yourName" required>
                    <div class="invalid-feedback">请输入用户名</div>
                </div>

                <div class="col-12">
                    <label for="yourPassword" class="form-label">密码</label>
                    <input type="password" name="password" class="form-control" id="yourPassword"
                           required>
                    <div class="invalid-feedback">请输入密码</div>
                </div>

                <div class="col-12">
                    <label for="confirmPassword" class="form-label">确认密码</label>
                    <input type="password" name="confirmPassword" class="form-control" id="confirmPassword"
                           required>
                    <div class="invalid-feedback">请再次输入密码</div>
                </div>


                <div class="col-12">
                    <div class="form-check">
                        <input class="form-check-input" name="terms" type="checkbox" value="" id="acceptTerms" required
                               checked>
                        <label class="form-check-label" for="acceptTerms">我同意<a href="#">条约</a></label>
                        <div class="invalid-feedback">您必须在提交前同意。</div>
                    </div>
                </div>
                <div class="col-12">
                    <button class="btn btn-primary w-100" type="submit">注册</button>
                </div>
                <div class="col-12">
                    <p class="small mb-0">已有帐户? <a href="{% url 'login' %}">前去登录</a></p>
                </div>
            </form>

        </div>
    </div>
{% endblock %}