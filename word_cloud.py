import jieba
from matplotlib import pylab as plt
from wordcloud import WordCloud
from PIL import Image
import numpy as np
import json
import os
import django
import re

os.environ.setdefault('DJANGO_SETTINGS_MODULE', '基于Django系统.settings')
django.setup()
from app.models import TravelInfo


def getIntroCloudImg(targetImgSrc, resImgSrc, field_name, exclude_words=None):
    travelList = TravelInfo.objects.all()
    text = ''
    if field_name == 'comments':
        for travel in travelList:
            comments = json.loads(travel.comments)
            for comm in comments:
                text += comm['content']
    else:
        for travel in travelList:
            # 根据字段名称动态获取字段值
            text += getattr(travel, field_name)

    cut = jieba.cut(text)
    string = ' '.join(cut)

    # 排除指定关键字
    if exclude_words:
        for word in exclude_words:
            string = re.sub(word, '', string)

    # 过滤掉长度为1的单词
    filtered_string = ' '.join(word for word in string.split() if len(word) > 1)

    # 如果需要使用图片形状，取消注释下面两行
    img = Image.open(targetImgSrc)
    img_arr = np.array(img)

    wc = WordCloud(
        scale=10,  # 提高 scale 提高词云的分辨率
        background_color='white',
        mask=img_arr,
        font_path='./飞波正点体.otf',  # 确保字体路径正确
        contour_color='black'  # 增加轮廓颜色，增强视觉效果
    )
    wc.generate_from_text(filtered_string)

    # 绘制图片
    plt.figure(figsize=(10, 10))  # 调整图像显示大小
    plt.imshow(wc, interpolation='bilinear')
    plt.axis('off')

    # 保存图像
    plt.savefig(resImgSrc, dpi=300, bbox_inches='tight', pad_inches=0.1)  # 高 dpi 提高清晰度
    # 关闭图形
    plt.close()


if __name__ == '__main__':
    exclude_words = []
    # 调用函数时传入字段名称，例如 'title' 或 'detailIntro'
    getIntroCloudImg('./static/img/2.png', './static/img/introCloud.jpg', 'detailIntro', exclude_words)
    getIntroCloudImg('./static/img/1.png', './static/img/commentContentCloud.jpg', 'comments')
    print('生成词云图成功~')
