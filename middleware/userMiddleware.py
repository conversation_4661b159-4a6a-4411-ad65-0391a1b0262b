from django.utils.deprecation import MiddlewareMixin
from django.shortcuts import render, redirect
from django.urls import reverse

class UserMiddleware(MiddlewareMixin):
    def process_request(self, request):
        path = request.path_info
        if path == '/app/login/' or path == '/app/register/':
            return None
        elif path.startswith('/admin/') or path == reverse('admin:index'):
            # 如果访问的是 Django 后台，直接返回 None，不进行登录检查
            return None
        else:
            # 对于其他路径进行登录检查
            if not request.session.get('username') or path == '/':
                return redirect('/app/login/')
            else:
                return None

    def process_view(self, request, callback, callback_args, callback_kwargs):
        pass

    def process_response(self, request, response):
        return response
