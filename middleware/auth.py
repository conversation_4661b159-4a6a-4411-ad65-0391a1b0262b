from django.utils.deprecation import MiddlewareMixin
from django.shortcuts import redirect

class AuthMiddleware(MiddlewareMixin):
    """
    登录验证中间件
    检查用户是否已登录，如果未登录则重定向到登录页面
    """
    def process_request(self, request):
        # 不需要登录验证的路径
        exempt_paths = ['/app/login/', '/app/register/', '/admin/']
        
        # 检查当前路径是否在豁免列表中
        for path in exempt_paths:
            if request.path_info.startswith(path):
                return None
        
        # 检查用户是否已登录
        if not request.session.get('username'):
            return redirect('/app/login/')
        
        return None 