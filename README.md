# Django万能项目模板

这是一个基于Django的项目模板，包含了用户注册、登录和基本信息管理功能，可以作为开发其他项目的基础。

## 功能特点

- 用户注册与登录
- 退出登录功能
- 个人信息管理
- 密码修改
- 美观的响应式界面

## 技术栈

- Django 3.1.14
- Bootstrap 5
- HTML/CSS/JavaScript

## 安装与使用

1. 克隆或下载项目代码

2. 安装依赖包
   ```
   pip install -r requirements.txt
   ```

3. 进行数据库迁移
   ```
   python manage.py makemigrations
   python manage.py migrate
   ```

4. 创建超级用户（可选）
   ```
   python manage.py createsuperuser
   ```

5. 启动开发服务器
   ```
   python manage.py runserver
   ```

6. 访问网站
   - 前台登录页面：http://127.0.0.1:8000/app/login/
   - 后台管理页面：http://127.0.0.1:8000/admin/

## 项目结构

- `app/` - 主应用目录
  - `models.py` - 数据模型
  - `views.py` - 视图函数
  - `urls.py` - URL路由
  - `templates/` - 应用模板
- `templates/` - 全局模板
- `static/` - 静态文件（CSS、JS、图片等）
- `media/` - 用户上传的媒体文件
- `middleware/` - 中间件

## 二次开发

您可以在此模板的基础上进行二次开发，添加更多功能模块。

1. 在 `app/models.py` 中添加新的数据模型
2. 在 `app/views.py` 中编写对应的视图函数
3. 在 `app/urls.py` 中添加新的URL路由
4. 在 `templates/` 目录下创建对应的模板文件

## 许可证

MIT 许可证 