from django.db import models

class User(models.Model):
    id = models.AutoField('id', primary_key=True)
    username = models.Char<PERSON><PERSON>('用户名', max_length=255, default='')
    password = models.Char<PERSON>ield('密码', max_length=255, default='')
    sex = models.Char<PERSON>ield('性别', max_length=255, default='')
    address = models.CharField('地址', max_length=255, default='')
    avatar = models.FileField('头像', upload_to='avatar', default='avatar/default.png')
    textarea = models.CharField('个人简介', max_length=255, default='这个人很懒，什么都没写...')
    createTime = models.DateTimeField('创建时间', auto_now_add=True)

    class Meta:
        verbose_name_plural = "前台用户"
        verbose_name = "前台用户"

    def __str__(self):
        return self.username