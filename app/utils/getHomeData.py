from app.utils import getPublicData
import random
import time

userData = getPublicData.getAllUsersInfoData()


def getUserCreateTimeData():
    dataDic = {}
    for user in userData:
        if dataDic.get(str(user.createTime)[:10], -1) == -1:
            dataDic[str(user.createTime)[:10]] = 1
        else:
            dataDic[str(user.createTime)[:10]] += 1
    # print(dataDic)
    resutData = []
    for key, value in dataDic.items():
        resutData.append({
            'name': key,
            'value': value
        })
    # print(dataDic)
    return resutData
