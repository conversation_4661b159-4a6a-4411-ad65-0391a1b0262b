from app.utils import getPublicData
import datetime

travelInfoList = getPublicData.getAllTravelInfoMapData()


def cityCharDataOne():
    cityDic = {}
    for travel in travelInfoList:
        if cityDic.get(travel.province, -1) == -1:
            cityDic[travel.province] = 1
        else:
            cityDic[travel.province] += 1

    return list(cityDic.keys()), list(cityDic.values())


def cityCharDataTwo():
    cityDic = {}
    for travel in travelInfoList:
        if cityDic.get(travel.level, -1) == -1:
            cityDic[travel.level] = 1
        else:
            cityDic[travel.level] += 1
    resultData = []
    for key, value in cityDic.items():
        resultData.append({
            'name': key,
            'value': value
        })
    return resultData


def getRateCharDataOne(travelInfoList):
    startDic = {}
    for travel in travelInfoList:
        if startDic.get(travel.star, -1) == -1:
            startDic[travel.star] = 1
        else:
            startDic[travel.star] += 1
    resultData = []
    for key, value in startDic.items():
        resultData.append({
            'name': key,
            'value': value
        })
    return resultData


def getRateCharDataTwo(travelInfoList):
    startDic = {}
    for travel in travelInfoList:
        if startDic.get(travel.score, -1) == -1:
            startDic[travel.score] = 1
        else:
            startDic[travel.score] += 1
    resultData = []
    for key, value in startDic.items():
        resultData.append({
            'name': key,
            'value': value
        })
    return resultData


def getPriceCharDataOne(travelList):
    xData = ['免费', '100元以内', '200元以内', '300元以内', '400元以内', '500元以内', '500元以外']
    yData = [0 for x in range(len(xData))]
    # yData = []
    # for x in range(len(xData)):
    #     yData.append(0)
    for travel in travelList:
        if travel.price != '':
            price = float(travel.price)
            if price <= 10:
                yData[0] += 1
            elif price <= 100:
                yData[1] += 1
            elif price <= 200:
                yData[2] += 1
            elif price <= 300:
                yData[3] += 1
            elif price <= 400:
                yData[4] += 1
            elif price <= 500:
                yData[5] += 1
            elif price > 500:
                yData[6] += 1
    return xData, yData


def getPriceCharDataTwo(travelList):
    xData = [str(x * 400) + '份以内' for x in range(1, 25)]
    yData = [0 for x in range(len(xData))]
    for travel in travelList:
        saleCount = float(travel.saleCount)
        for x in range(1, 25):
            count = x * 400
            if saleCount <= count:
                yData[x - 1] += 1
                break
    return xData, yData


def getPriceCharDataTrhee(travelList):
    startDic = {}
    for travel in travelInfoList:
        if startDic.get(travel.discount, -1) == -1:
            startDic[travel.discount] = 1
        else:
            startDic[travel.discount] += 1
    resultData = []
    for key, value in startDic.items():
        resultData.append({
            'name': key,
            'value': value
        })
    return resultData


def getCommentsCharData0ne():
    commentsList = getPublicData.getAllCommentsData()

    xData = []

    def get_list(date):
        return datetime.datetime.strptime(date, '%Y-%m-%d').timestamp()

    for comment in commentsList:
        xData.append(comment['date'])
    xData = list(set(xData))
    xData = list(sorted(xData, key=lambda x: get_list(x), reverse=True))
    yData = [0 for x in range(len(xData))]
    for comment in commentsList:
        for index, date in enumerate(xData):
            if comment['date'] == date:
                yData[index] += 1

    return xData, yData


# def getCommentsCharData0ne():
#     commentsList = getPublicData.getAllCommentsData()
#
#     xData = []
#     timestamps = []
#
#     def get_list(date):
#         return datetime.datetime.strptime(date, '%Y-%m-%d').timestamp()
#
#     for comment in commentsList:
#         date = comment['date']
#         timestamp = get_list(date)
#         if timestamp not in timestamps:
#             xData.append(date)
#             timestamps.append(timestamp)
#
#     # 排序 xData，按照时间戳降序
#     xData.sort(key=get_list, reverse=True)
#
#     yData = [0 for _ in xData]
#     for comment in commentsList:
#         date = comment['date']
#         # 由于已经排序，可以使用二分查找来优化查找性能
#         left, right = 0, len(xData) - 1
#         while left <= right:
#             mid = (left + right) // 2
#             if get_list(xData[mid]) == get_list(date):
#                 yData[mid] += 1
#                 break
#             elif get_list(xData[mid]) < get_list(date):
#                 right = mid - 1
#             else:
#                 left = mid + 1
#
#     return xData, yData


def getCommentsCharDataTwo():
    commentsList = getPublicData.getAllCommentsData()
    startDic = {}
    for travel in commentsList:
        score = travel['score']
        if score != 0:  # 排除评分为0的评论
            if startDic.get(score, -1) == -1:
                startDic[score] = 1
            else:
                startDic[score] += 1
    resultData = []
    for key, value in startDic.items():
        resultData.append({
            'name': key,
            'value': value
        })
    return resultData



def getPriceCharDataThree():
    travelList = getPublicData.getAllTravelInfoMapData()
    xData = [str(x * 1000) + '条以内' for x in range(1, 15)]
    yData = [0 for x in range(len(xData))]
    for travel in travelList:
        saleCount = int(travel.commentsLen)
        for x in range(1, 15):
            count = x * 1000
            if saleCount <= count:
                yData[x - 1] += 1
                break
    return xData, yData
