from app.utils import getPublicData
import random
import json

travelInfoList = getPublicData.getAllTravelInfoMapData()


def getAllTravelByTitle(recommended_items):
    def map_fn(item):
        item.img_list = json.loads(item.img_list)
        item.comments = json.loads(item.comments)
        return item

    if recommended_items:
        travelList = TravelInfo.objects.filter(title__in=recommended_items)

    travelListMap = list(map(map_fn, travelList))
    return travelListMap


def getRandomTravel(number_of_travels=10):
    # 确保请求的数量不大于列表的长度
    number_of_travels = min(number_of_travels, len(travelInfoList))
    # 随机选择指定数量的旅行目的地
    random_travels = random.sample(travelInfoList, number_of_travels)
    # 返回随机选择的旅行目的地列表
    return random_travels
