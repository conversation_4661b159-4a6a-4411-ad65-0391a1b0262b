from django.shortcuts import render, redirect
from app.models import User
from django.http import HttpResponse
from app.utils import errorResponse, getHomeData, getPublicData, getChangeSelfInfoData

# 登陆
def login(request):
    if request.method == 'GET':
        return render(request, 'login.html')

    elif request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        try:
            User.objects.get(username=username, password=password)
            request.session['username'] = username
            return redirect('/app/home')
        except:
            return errorResponse.errorResponse(request, '用户名或密码错误')


# 注册
def register(request):
    if request.method == 'GET':
        return render(request, 'register.html')
    elif request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        confirmPassword = request.POST.get('confirmPassword')

        if not username or not password or not confirmPassword:
            return errorResponse.errorResponse(request, '不允许为空值')

        if password != confirmPassword:
            return errorResponse.errorResponse(request, '两次密码不一致')

        try:
            User.objects.get(username=username)
            return errorResponse.errorResponse(request, '该账号已存在')
        except User.DoesNotExist:
            # 如果用户不存在，创建新用户
            User.objects.create(username=username, password=password)
            return redirect('/app/login')

    return errorResponse.errorResponse(request, '该账号已存在')


# 退出登陆
def logOut(request):
    # request.session.clear()
    request.session.flush()  # 使用flush()方法彻底清除会话
    return redirect('/app/login')


# 首页
def home(request):
    username = request.session.get('username')
    userInfo = User.objects.get(username=username)

    return render(request, 'home.html', {
        'userInfo': userInfo,
    })


# 个人信息
def changeSelfInfo(request):
    username = request.session.get('username')
    userInfo = User.objects.get(username=username)
    

    if request.method == 'POST':
        getChangeSelfInfoData.changeSelfInfo(username, request.POST, request.FILES)
        userInfo = User.objects.get(username=username)
        return redirect('/app/home')

    return render(request, 'changeSelfInfo.html', {
        'userInfo': userInfo,

    })


def changePassword(request):
    username = request.session.get('username')
    userInfo = User.objects.get(username=username)
    

    if request.method == 'POST':
        res = getChangeSelfInfoData.changePassword(userInfo, request.POST)
        if res != None:
            return errorResponse.errorResponse(request, res)
        else:
            return redirect('/app/home')

    return render(request, 'changePassword.html', {
        'userInfo': userInfo,

    })
