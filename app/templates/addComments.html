{% extends 'base.html' %}

{% block title %}
    添加评论页面
{% endblock %}

{% block sidebar %}
    <aside id="sidebar" class="sidebar">

        <ul class="sidebar-nav" id="sidebar-nav">

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'home' %}">
                    <i class="bi bi-grid"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-heading">个人信息</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'changeSelfInfo' %}">
                    <i class="bi bi-menu-button-wide"></i><span>修改信息</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'changePassword' %}">
                    <i class="bi bi-journal-text"></i><span>修改密码</span>
                </a>
            </li>

            <li class="nav-heading">数据表格</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'tableData' %}">
                    <i class="bi bi-layout-text-window-reverse"></i><span>数据操作</span>
                </a>
            </li>


            <li class="nav-heading">数据可视化</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'cityChar' %}">
                    <i class="bi bi-bar-chart"></i><span>城市和景点等级分析</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'rateChar' %}">
                    <i class="bi bi-gem"></i><span>评分情况分析</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'priceChar' %}">
                    <i class="bi bi-graph-up"></i><span>价格和月销量分析</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'commentsChar' %}">
                    <i class="bi bi-easel-fill"></i><span>评论分析</span>
                </a>
            </li>


            <li class="nav-heading">推荐算法</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'recommendation' %}">
                    <i class="bi bi-hurricane"></i><span>猜你喜欢</span>
                </a>
            </li>

            <li class="nav-heading">词云图</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'detailIntroCloud' %}">
                    <i class="bi bi-image"></i><span>详情简介词云图</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'commentContentCloud' %}">
                    <i class="bi bi-image-fill"></i><span>评论内容词云图</span>
                </a>
            </li>


        </ul>

    </aside><!-- End Sidebar-->
{% endblock %}

{% block content %}
    <div class="pagetitle" style="display: flex;align-items: center">
        <div style="margin-right: auto">
            <h1>添加评论</h1>
            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'changeSelfInfo' %}">数据表格</a></li>
                    <li class="breadcrumb-item active">添加评论页面</li>
                </ol>
            </nav>
        </div>
        <h5>
            {{ nowTime.year }} - {{ nowTime.mon }} - {{ nowTime.day }}
        </h5>
    </div><!-- End Page Title -->

    <section class="section dashboard">
        <div class="row">

            <!-- Right side columns -->
            <div class="col-lg-12">

                <!-- Recent Activity -->
                <div class="card">

                    <div class="card-body">
                        <h5 class="card-title">添加评论表格</h5>


                        <form action="{% url 'addComments' id %}" method="post" enctype="multipart/form-data">

                            <div class="activity">

                                <div class="row mb-3">
                                    <label for="inputText" class="col-sm-2 col-form-label">旅游地</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="username" disabled value="{{ travelInfo.title }}"
                                               class="form-control">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <label for="inputText" class="col-sm-2 col-form-label">详情地址</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="address" disabled
                                               value="{{ travelInfo.detailAddress }}"
                                               class="form-control">
                                    </div>
                                </div>


                                <div class="row mb-3">
                                    <label for="inputText" class="col-sm-2 col-form-label">封面图片</label>
                                    <div class="col-sm-10">
                                        <img style="width:300px; border: 1px solid #ddd; padding: 5px;"
                                             src="{{ travelInfo.cover }}" alt="">
                                        <br>
                                    </div>
                                </div>

                                <style>

                                    .start-container {
                                        font-size: 0;
                                    }

                                    .start {
                                        font-size: 24px; /* 星号大小，可根据需要调整 */
                                        cursor: pointer;
                                        color: gray; /* 星号颜色，可根据需要调整 */
                                        display: inline-block;
                                        transition: color 0.3s;
                                    }


                                    /* 鼠标悬停时改变星号颜色，可选 */
                                    .start.checked {
                                        color: gold;
                                    }
                                </style>

                                <script>

                                    let currentRating = 0

                                    function reteStart(rating) {
                                        currentRating = rating
                                        // 将选中的评分值写入隐藏的输入框中
                                        document.getElementById('rate').value = rating;
                                        // 取消所有星号的选中状态
                                        const starts = document.querySelectorAll('.start');
                                        starts.forEach(function (item, index) {
                                            if (index < rating) {
                                                item.classList.add('checked');
                                            } else {
                                                item.classList.remove('checked');
                                            }
                                        });
                                    }
                                </script>


                                <div class="row mb-3">
                                    <label for="inputText" class="col-sm-2 col-form-label">评分选择</label>
                                    <div class="col-sm-10 start-container">
                                        <div class="start" onclick="reteStart(1)">★</div>
                                        <div class="start" onclick="reteStart(2)">★</div>
                                        <div class="start" onclick="reteStart(3)">★</div>
                                        <div class="start" onclick="reteStart(4)">★</div>
                                        <div class="start" onclick="reteStart(5)">★</div>

                                        <input type="text" name="rate" id="rate" style="display:none;">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <label for="inputText" class="col-sm-2 col-form-label">评论内容</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="content" placeholder="请输入对该景点要评论的内容" class="form-control">
                                    </div>
                                </div>


                                <div class="row mb-3">
                                    <label for="inputText" class="col-sm-2 col-form-label"></label>
                                    <div class="col-sm-10">
                                        <button type="submit" class="btn btn-primary">提交</button>
                                    </div>
                                </div>

                            </div>


                        </form>


                    </div>
                </div>


            </div>

        </div>
    </section>
{% endblock %}

{% block echarts %}
    <script>

    </script>
{% endblock %}

