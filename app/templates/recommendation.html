{% extends 'base.html' %}

{% block title %}
    景点推荐
{% endblock %}

{% block sidebar %}
    <aside id="sidebar" class="sidebar">

        <ul class="sidebar-nav" id="sidebar-nav">

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'home' %}">
                    <i class="bi bi-grid"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-heading">个人信息</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'changeSelfInfo' %}">
                    <i class="bi bi-menu-button-wide"></i><span>修改信息</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'changePassword' %}">
                    <i class="bi bi-journal-text"></i><span>修改密码</span>
                </a>
            </li>

            <li class="nav-heading">数据表格</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'tableData' %}">
                    <i class="bi bi-layout-text-window-reverse"></i><span>数据操作</span>
                </a>
            </li>


            <li class="nav-heading">数据可视化</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'cityChar' %}">
                    <i class="bi bi-bar-chart"></i><span>城市和景点等级分析</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'rateChar' %}">
                    <i class="bi bi-gem"></i><span>评分情况分析</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'priceChar' %}">
                    <i class="bi bi-graph-up"></i><span>价格和月销量分析</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'commentsChar' %}">
                    <i class="bi bi-easel-fill"></i><span>评论分析</span>
                </a>
            </li>


            <li class="nav-heading">推荐算法</li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'recommendation' %}">
                    <i class="bi bi-hurricane"></i><span>猜你喜欢</span>
                </a>
            </li>

            <li class="nav-heading">词云图</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'detailIntroCloud' %}">
                    <i class="bi bi-image"></i><span>详情简介词云图</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'commentContentCloud' %}">
                    <i class="bi bi-image-fill"></i><span>评论内容词云图</span>
                </a>
            </li>


        </ul>

    </aside><!-- End Sidebar-->
{% endblock %}


{% block content %}
    <div class="pagetitle" style="display: flex;align-items: center">
        <div style="margin-right: auto">
            <h1>景点推荐</h1>
            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'changeSelfInfo' %}">数据可视化</a></li>
                    <li class="breadcrumb-item active">景点推荐</li>
                </ol>
            </nav>
        </div>
        <h5>
            {{ nowTime.year }} - {{ nowTime.mon }} - {{ nowTime.day }}
        </h5>
    </div><!-- End Page Title -->

    <section class="section dashboard">
        <style>
            .image-container {
                display: flex;
                flex-wrap: wrap; /* 允许换行 */
                justify-content: flex-start; /* 水平排列在容器的开始位置 */
                align-items: flex-start; /* 如果需要垂直对齐到顶部 */
            }

            .image-item {
                flex: 0 0 auto; /* 允许自动宽度，不伸缩 */
                margin-right: 10px; /* 给图片之间添加一些间隔 */
                margin-bottom: 10px; /* 给图片底部添加间隔，防止超出 .row 容器 */
            }
        </style>

        <div class="row">
            {% for  travelInfo in resultDataList %}

                <!-- Right side columns -->
                <div class="col-lg-4">

                    <!-- Recent Activity -->
                    <div class="card">


                        <div class="card-body">
                            <h5 class="card-title">{{ travelInfo.title }}</h5>
                            <div id="mains" style="width: 100%;height: 450px">

                                <div class="activity">

                                    <div class="row mb-1">
                                        <div class="col-sm-10">
                                            <a href="{{ travelInfo.detailUrl }}">
                                                <img style="width:300px;height: 220px; border: 1px solid #ddd; padding: 5px;"
                                                     src="{{ travelInfo.cover }}" alt="">
                                            </a>
                                        </div>
                                    </div>


{#                                    <div class="row mb-1">#}
{#                                        <label for="inputText">评分: <span#}
{#                                                class="badge bg-primary">{{ travelInfo.score }}分</span>#}
{#                                        </label>#}
{#                                    </div>#}

                                    <div class="row mb-1">
                                        <label for="inputText">评分:
                                            <span class="badge bg-primary">
                                                {% if travelInfo.score %}
                                                    {{ travelInfo.score }}分
                                                {% else %}
                                                    暂无
                                                {% endif %}
                                            </span>
                                        </label>
                                    </div>



                                    <div class="row mb-1">
                                        <label for="inputText">等级: <span
                                                style="color: orange">{{ travelInfo.level }}</span></label>
                                    </div>

                                    <div class="row mb-1">
                                        <label for="inputText">价格: <span style="color: green">
                                            {% if travelInfo.price %}
                                                ${{ travelInfo.price }}元
                                            {% else %}
                                                暂无
                                            {% endif %}
                                        </span></label>

                                    </div>
                                    <div class="row mb-1">
                                        <label for="inputText">详细地址:{{ travelInfo.detailAddress }}</label>
                                    </div>
                                </div>


                                <div class="row mb-3">
                                    <div class="image-container">
                                        {% for i in travelInfo.img_list %}
                                            <div class="image-item">
                                                <img style="width: 80px; height: auto; border: 1px solid #ddd; padding: 5px; display: block;"
                                                     src="{{ i }}" alt="">
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>


                            </div>

                        </div>


                    </div>
                </div>
            {% endfor %}

        </div>


    </section>
{% endblock %}

{% block echarts %}
    <script>

    </script>
{% endblock %}

