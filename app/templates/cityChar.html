{% extends 'base.html' %}

{% block title %}
    城市和景点等级分析
{% endblock %}

{% block sidebar %}
    <aside id="sidebar" class="sidebar">

        <ul class="sidebar-nav" id="sidebar-nav">

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'home' %}">
                    <i class="bi bi-grid"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-heading">个人信息</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'changeSelfInfo' %}">
                    <i class="bi bi-menu-button-wide"></i><span>修改信息</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'changePassword' %}">
                    <i class="bi bi-journal-text"></i><span>修改密码</span>
                </a>
            </li>

            <li class="nav-heading">数据表格</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'tableData' %}">
                    <i class="bi bi-layout-text-window-reverse"></i><span>数据操作</span>
                </a>
            </li>


            <li class="nav-heading">数据可视化</li>
            <li class="nav-item">
                <a class="nav-link " href="{% url 'cityChar' %}">
                    <i class="bi bi-bar-chart"></i><span>城市和景点等级分析</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'rateChar' %}">
                    <i class="bi bi-gem"></i><span>评分情况分析</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'priceChar' %}">
                    <i class="bi bi-graph-up"></i><span>价格和月销量分析</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'commentsChar' %}">
                    <i class="bi bi-easel-fill"></i><span>评论分析</span>
                </a>
            </li>


            <li class="nav-heading">推荐算法</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'recommendation' %}">
                    <i class="bi bi-hurricane"></i><span>猜你喜欢</span>
                </a>
            </li>

            <li class="nav-heading">词云图</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'detailIntroCloud' %}">
                    <i class="bi bi-image"></i><span>详情简介词云图</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'commentContentCloud' %}">
                    <i class="bi bi-image-fill"></i><span>评论内容词云图</span>
                </a>
            </li>


        </ul>

    </aside><!-- End Sidebar-->
{% endblock %}


{% block content %}
    <div class="pagetitle" style="display: flex;align-items: center">
        <div style="margin-right: auto">
            <h1>城市和景点等级分析</h1>
            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'changeSelfInfo' %}">数据可视化</a></li>
                    <li class="breadcrumb-item active">城市和景点等级分析</li>
                </ol>
            </nav>
        </div>
        <h5>
            {{ nowTime.year }} - {{ nowTime.mon }} - {{ nowTime.day }}
        </h5>
    </div><!-- End Page Title -->

    <section class="section dashboard">
        <div class="row">

            <!-- Right side columns -->
            <div class="col-lg-6">

                <!-- Recent Activity -->
                <div class="card">

                    <div class="card-body">
                        <h5 class="card-title">城市个数柱状图</h5>
                        <div id="mains" style="width: 100%;height: 450px"></div>

                    </div>
                </div>


            </div>


            <!-- Right side columns -->
            <div class="col-lg-6">

                <!-- Recent Activity -->
                <div class="card">

                    <div class="card-body">
                        <h5 class="card-title">景点等级情况</h5>
                        <div id="mainsTwo" style="width: 100%;height: 450px"></div>

                    </div>
                </div>


            </div>


        </div>
    </section>
{% endblock %}

{% block echarts %}
    <script>
        var chartDom = document.getElementById('mains');
        var myChart = echarts.init(chartDom);
        var option;

        option = {
            title: {
                text: '各城市景点个数占比柱状图',
                subtext: 'Fake Data'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['各城市景点个数']
            },
            toolbox: {
                show: true,
                feature: {
                    dataView: {show: true, readOnly: false},
                    magicType: {show: true, type: ['line', 'bar']},
                    restore: {show: true},
                    saveAsImage: {show: true}
                }
            },
            calculable: true,
            xAxis: [
                {
                    type: 'category',
                    // prettier-ignore
                    data: {{ cityCharOneData.Xdata | safe }}
                }
            ],
            yAxis: [
                {
                    type: 'value'
                }
            ],
            dataZoom: [
                {
                    show: true,
                    start: 0,
                    end: 20
                },
            ],
            series: [
                {
                    {#itemStyle: {#}
                    {#    color: '#000'#}
                    {#    },#}
                    name: '各城市景点个数',
                    type: 'bar',
                    data: {{ cityCharOneData.Ydata }},
                    markPoint: {
                        data: [
                            {type: 'max', name: 'Max'},
                            {type: 'min', name: 'Min'}
                        ]
                    },
                    markLine: {
                        data: [{type: 'average', name: 'Avg'}]
                    }

                }
            ]
        };

        option && myChart.setOption(option);
    </script>

    <script>
        var chartDom = document.getElementById('mainsTwo');
        var myChart = echarts.init(chartDom);
        var option;

        option = {
            tooltip: {
                trigger: 'item'
            },
            legend: {
                top: '5%',
                left: 'center'
            },
            label: {
                show: true,
                position: 'inside',
                formatter: '{b}: {d}%\n({c})' // 显示百分比和数量
            },
            series: [
                {
                    name: '',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 22,
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: {{ cityCharTowData | safe }}
                }
            ]
        };

        option && myChart.setOption(option);
    </script>
{% endblock %}

