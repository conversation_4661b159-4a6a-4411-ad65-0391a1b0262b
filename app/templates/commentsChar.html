{% extends 'base.html' %}

{% block title %}
    评论分析图
{% endblock %}

{% block sidebar %}
    <aside id="sidebar" class="sidebar">

        <ul class="sidebar-nav" id="sidebar-nav">

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'home' %}">
                    <i class="bi bi-grid"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-heading">个人信息</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'changeSelfInfo' %}">
                    <i class="bi bi-menu-button-wide"></i><span>修改信息</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'changePassword' %}">
                    <i class="bi bi-journal-text"></i><span>修改密码</span>
                </a>
            </li>

            <li class="nav-heading">数据表格</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'tableData' %}">
                    <i class="bi bi-layout-text-window-reverse"></i><span>数据操作</span>
                </a>
            </li>


            <li class="nav-heading">数据可视化</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'cityChar' %}">
                    <i class="bi bi-bar-chart"></i><span>城市和景点等级分析</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'rateChar' %}">
                    <i class="bi bi-gem"></i><span>评分情况分析</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'priceChar' %}">
                    <i class="bi bi-graph-up"></i><span>价格和月销量分析</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link " href="{% url 'commentsChar' %}">
                    <i class="bi bi-easel-fill"></i><span>评论分析</span>
                </a>
            </li>


            <li class="nav-heading">推荐算法</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'recommendation' %}">
                    <i class="bi bi-hurricane"></i><span>猜你喜欢</span>
                </a>
            </li>

            <li class="nav-heading">词云图</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'detailIntroCloud' %}">
                    <i class="bi bi-image"></i><span>详情简介词云图</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'commentContentCloud' %}">
                    <i class="bi bi-image-fill"></i><span>评论内容词云图</span>
                </a>
            </li>


        </ul>

    </aside><!-- End Sidebar-->
{% endblock %}


{% block content %}
    <div class="pagetitle" style="display: flex;align-items: center">
        <div style="margin-right: auto">
            <h1>评论分析图</h1>
            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'changeSelfInfo' %}">数据可视化</a></li>
                    <li class="breadcrumb-item active">评论分析页面</li>
                </ol>
            </nav>
        </div>
        <h5>
            {{ nowTime.year }} - {{ nowTime.mon }} - {{ nowTime.day }}
        </h5>
    </div><!-- End Page Title -->

    <section class="section dashboard">


        <div class="row">

            <!-- Right side columns -->
            <div class="col-lg-8">

                <!-- Recent Activity -->
                <div class="card">

                    <div class="card-body">
                        <h5 class="card-title">评论时间分析表</h5>
                        <div id="mains" style="width: 100%;height: 450px"></div>

                    </div>
                </div>


            </div>


            <!-- Right side columns -->
            <div class="col-lg-4">

                <!-- Recent Activity -->
                <div class="card">

                    <div class="card-body">
                        <h5 class="card-title">评论评分星级占比</h5>
                        <div id="mainsTwo" style="width: 100%;height: 450px"></div>

                    </div>
                </div>


            </div>


            <div class="col-lg-12">

                <!-- Recent Activity -->
                <div class="card">

                    <div class="card-body">
                        <h5 class="card-title">评论个数分析图</h5>
                        <div id="mainsThree" style="width: 100%;height: 450px"></div>

                    </div>
                </div>


            </div>

        </div>
    </section>
{% endblock %}

{% block echarts %}
    <script>
        var chartDom = document.getElementById('mains');
        var myChart = echarts.init(chartDom);
        var option;

        myChart.setOption(
            (option = {
                title: {
                    text: '评论时间个数折线图',
                    left: '1%'
                },
                tooltip: {
                    trigger: 'axis'
                },
                grid: {
                    left: '15%',
                    right: '15%',
                    bottom: '15%'
                },
                xAxis: {
                    data: {{ commentschartsData.xData | safe }}
                },
                yAxis: {},
                toolbox: {
                    right: 10,
                    feature: {
                        dataZoom: {
                            yAxisIndex: 'none'
                        },
                        restore: {},
                        saveAsImage: {}
                    }
                },
                dataZoom: [
                    {
                        type: 'slider', // 支持 'inside' 和 'slider'
                        start: 0,      // 起始位置（0表示头部）
                        end: 50,      // 结束位置（100表示尾部）
                    },
                ],
                visualMap: {
                    top: 50,
                    right: 10,
                    pieces: [
                        {
                            gt: 0,
                            lte: 20,
                            color: '#93CE07'
                        },
                        {
                            gt: 20,
                            lte: 40,
                            color: '#FBDB0F'
                        },
                        {
                            gt: 40,
                            lte: 60,
                            color: '#FC7D02'
                        },
                        {
                            gt: 60,
                            lte: 100,
                            color: '#FD0100'
                        },
                        {
                            gt: 100,
                            lte: 200,
                            color: '#AA069F'
                        },
                    ],
                    outOfRange: {
                        color: '#999'
                    }
                },
                series: {
                    name: '评论时间个数',
                    type: 'line',
                    data: {{ commentschartsData.yData | safe }},
                    markLine: {
                        silent: true,
                        lineStyle: {
                            color: '#333'
                        },
                        data: [
                            {
                                yAxis: 50
                            },
                            {
                                yAxis: 100
                            },
                            {
                                yAxis: 150
                            },
                            {
                                yAxis: 200
                            },
                            {
                                yAxis: 300
                            }
                        ]
                    }
                }
            })
        );


        option && myChart.setOption(option);
    </script>

    <script>
        var chartDom = document.getElementById('mainsTwo');
        var myChart = echarts.init(chartDom);
        var option;

        option = {
            title: {
                text: '评论星级个数',
                left: 'center'
            },
            tooltip: {
                trigger: 'item'
            },
            legend: {
                orient: 'vertical',
                left: 'left'
            },
            series: [
                {
                    name: '评论星级个数',
                    type: 'pie',
                    radius: '50%',
                    data: {{ commentsScorePieData | safe }},
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ]
        };

        option && myChart.setOption(option);
    </script>

    <script>
        var chartDom = document.getElementById('mainsThree');
        var myChart = echarts.init(chartDom);
        var option;

        // 假设 commentschartsData.x1Data 和 commentschartsData.y1Data 是从后端模板渲染得到的安全数据
        let dataAxis = {{ commentschartsData.x1Data | safe }};
        let data = {{ commentschartsData.y1Data | safe }};
        let yMax = Math.max(...data);
        let dataShadow = [];
        for (let i = 0; i < data.length; i++) {
            dataShadow.push(yMax);
        }

        option = {
            title: {
                text: '各景点评论总量区间分析图',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                bottom: '15%', // 保证底部有足够的空间显示轴标签
                containLabel: true
            },
            xAxis: {
                data: dataAxis,
                axisLabel: {
                    inside: true,
                    color: '#fff',
                    formatter: function (value) {
                        return value.length > 5 ? value.substring(0, 5) + '...' : value;
                    },
                    align: 'center', // 标签对齐方式
                    margin: 0, // 增加标签与轴线的距离，使标签更靠近轴下方
                    interval: 'auto' // 自动根据标签长度和空间决定是否显示所有标签
                },
                axisTick: {
                    show: false
                },
                axisLine: {
                    show: false
                },
                z: 10
            },
            yAxis: {
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    color: '#999'
                },
                max: yMax // 设置 y 轴的最大值
            },
            dataZoom: [
                {
                    type: 'slider', // 支持 'inside' 和 'slider'
                    start: 0,      // 起始位置（0表示头部）
                    end: 50,      // 结束位置（100表示尾部）
                },
                {
                    start: 0,
                    end: 10,
                    handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                    handleSize: '80%',
                    handleStyle: {
                        color: '#fff',
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.6)',
                        shadowOffsetX: 2,
                        shadowOffsetY: 2
                    }
                }
            ],
            series: [
                {
                    type: 'bar',
                    showBackground: true,
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(
                            0, 0, 0, 1,
                            [
                                {offset: 0, color: '#83bff6'},
                                {offset: 0.5, color: '#188df0'},
                                {offset: 1, color: '#188df0'}
                            ]
                        )
                    },
                    emphasis: {
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(
                                0, 0, 0, 1,
                                [
                                    {offset: 0, color: '#2378f7'},
                                    {offset: 0.7, color: '#2378f7'},
                                    {offset: 1, color: '#83bff6'}
                                ]
                            )
                        }
                    },
                    data: data,
                    label: {
                        normal: {
                            show: true,
                            position: 'top', // 在柱子的顶部显示标签
                            formatter: '{c}', // 使用柱子的值作为标签内容
                            color: '#000' // 标签文本颜色
                        }
                    }
                }
            ]
        };

        // Enable data zoom when user click bar.
        myChart.on('click', function (params) {
            console.log(dataAxis[Math.max(params.dataIndex - zoomSize / 2, 0)]);
            myChart.dispatchAction({
                type: 'dataZoom',
                startValue: dataAxis[Math.max(params.dataIndex - zoomSize / 2, 0)],
                endValue: dataAxis[Math.min(params.dataIndex + zoomSize / 2, data.length - 1)]
            });
        });

        option && myChart.setOption(option);
    </script>
{% endblock %}

