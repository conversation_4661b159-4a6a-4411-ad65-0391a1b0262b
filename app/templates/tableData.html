{% extends 'base.html' %}

{% block title %}
    数据操作页面
{% endblock %}

{% block sidebar %}
    <aside id="sidebar" class="sidebar">

        <ul class="sidebar-nav" id="sidebar-nav">

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'home' %}">
                    <i class="bi bi-grid"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-heading">个人信息</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'changeSelfInfo' %}">
                    <i class="bi bi-menu-button-wide"></i><span>修改信息</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'changePassword' %}">
                    <i class="bi bi-journal-text"></i><span>修改密码</span>
                </a>
            </li>

            <li class="nav-heading">数据表格</li>
            <li class="nav-item">
                <a class="nav-link " href="{% url 'tableData' %}">
                    <i class="bi bi-layout-text-window-reverse"></i><span>数据操作</span>
                </a>
            </li>


            <li class="nav-heading">数据可视化</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'cityChar' %}">
                    <i class="bi bi-bar-chart"></i><span>城市和景点等级分析</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'rateChar' %}">
                    <i class="bi bi-gem"></i><span>评分情况分析</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'priceChar' %}">
                    <i class="bi bi-graph-up"></i><span>价格和月销量分析</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'commentsChar' %}">
                    <i class="bi bi-easel-fill"></i><span>评论分析</span>
                </a>
            </li>


            <li class="nav-heading">推荐算法</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'recommendation' %}">
                    <i class="bi bi-hurricane"></i><span>猜你喜欢</span>
                </a>
            </li>

            <li class="nav-heading">词云图</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'detailIntroCloud' %}">
                    <i class="bi bi-image"></i><span>详情简介词云图</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'commentContentCloud' %}">
                    <i class="bi bi-image-fill"></i><span>评论内容词云图</span>
                </a>
            </li>


        </ul>

    </aside><!-- End Sidebar-->
{% endblock %}


{% block content %}
    <div class="pagetitle" style="display: flex;align-items: center">
        <div style="margin-right: auto">
            <h1>数据操作</h1>
            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'changeSelfInfo' %}">数据表格</a></li>
                    <li class="breadcrumb-item active">数据操作页面</li>
                </ol>
            </nav>
        </div>
        <h5>
            {{ nowTime.year }} - {{ nowTime.mon }} - {{ nowTime.day }}
        </h5>
    </div><!-- End Page Title -->

    <section class="section dashboard">

        <div class="row">
            <!-- Right side columns -->
            <div class="col-lg-12">

                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">景点数据表格</h5>

                        <!-- 搜索表单 -->
                        <form method="GET" action="{% url 'tableData' %}" style="display: flex;">
                            <div class="col-sm-2">
                                <input type="text" name="search" class="form-control"
                                       placeholder="输入关键词搜索">
                            </div>
                            <button type="submit" class="btn btn-primary waves-effect waves-light"
                                    style="margin-left: 10px;">搜索
                            </button>
                        </form>

                        <!-- 表格数据 -->
                        <table class="table table-striped table-hover"
                               style="text-align: center;vertical-align: middle;">
                            <!-- 表头 -->
                            <thead>
                            <tr>
                                <th scope="col">id</th>
                                <th scope="col">景区名</th>
                                <th style="width: 100px" scope="col">景区等级</th>
                                <th style="width: 100px" scope="col">景区省份</th>
                                <th style="width: 100px" scope="col">景区评分</th>
                                <th style="width: 100px" scope="col">价格</th>
                                <th style="width: 150px" scope="col">月销量</th>
                                <th style="width: 200px" scope="col">图片</th>
                                <th style="width: 150px" scope="col">操作</th>
                            </tr>
                            </thead>
                            <!-- 表格内容 -->
                            <tbody>
                            {% for item in page_obj %}
                                <tr>
                                    <th scope="row">{{ item.id }}</th>
                                    <td>{{ item.title }}</td>
                                    <td><a href="#">{{ item.level }}</a></td>
                                    <td>{{ item.province }}</td>
                                    <td><span class="badge bg-success">
                                        {% if item.score %}
                                            {{ item.score }}分
                                        {% else %}
                                            暂无
                                        {% endif %}
                                    </span></td>
                                    <td style="color: orange">
                                        {% if item.price %}
                                            ${{ item.price }}元
                                        {% else %}
                                            暂无
                                        {% endif %}
                                    </td>
                                    <td>{{ item.saleCount }}/月销量</td>
                                    <td>
                                        <a href="{{ item.detailUrl }}">
                                            <img style="width: 100%;height: 150px" src="{{ item.cover }}" alt="">
                                        </a>
                                    </td>
                                    <td style="text-align: center;line-height: 70px">
                                        <a href="{% url 'addComments' item.id %}"
                                           class="btn btn-primary waves-effect waves-light">
                                            添加评论
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>

                        <!-- 分页链接 -->
                        <div class="pagination">
                            <span class="step-links">
                                {% if page_obj.has_previous %}
                                    <a href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">&laquo; 第一页</a>
                                    <a href="?page=






                                            {{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">上一页</a>
                                {% endif %}
                                <span class="current">
                                    第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页
                                </span>
                                {% if page_obj.has_next %}
                                    <a href="?page=






                                            {{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">下一页</a>
                                    <a href="?page=






                                            {{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">最后一页 &raquo;</a>
                                {% endif %}
                            </span>
                        </div>


                    </div>
                </div>
            </div>


        </div>

    </section>
{% endblock %}

{% block echarts %}
    <script>

    </script>
{% endblock %}

