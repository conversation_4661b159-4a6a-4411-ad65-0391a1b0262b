{% extends 'base.html' %}

{% block title %}首页{% endblock %}

{% block content %}
<div class="pagetitle" style="display: flex;align-items: center">
    <div style="margin-right: auto">
        <h1>首页</h1>
        <nav>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'home' %}">首页</a></li>
                <li class="breadcrumb-item active">仪表盘</li>
            </ol>
        </nav>
    </div>

</div>

<section class="section dashboard">
    <div class="row">
        <!-- 统计卡片 -->
        <div class="col-xxl-4 col-md-6">
            <div class="card info-card sales-card">
                <div class="card-body">
                    <h5 class="card-title">景点总数 <span>| 全国</span></h5>
                    <div class="d-flex align-items-center">
                        <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-geo-alt"></i>
                        </div>
                        <div class="ps-3">
                            <h6>1,458</h6>
                            <span class="text-success small pt-1 fw-bold">12%</span> <span class="text-muted small pt-2 ps-1">较上月增长</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xxl-4 col-md-6">
            <div class="card info-card revenue-card">
                <div class="card-body">
                    <h5 class="card-title">城市覆盖 <span>| 全国</span></h5>
                    <div class="d-flex align-items-center">
                        <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-building"></i>
                        </div>
                        <div class="ps-3">
                            <h6>64</h6>
                            <span class="text-success small pt-1 fw-bold">8%</span> <span class="text-muted small pt-2 ps-1">本季度新增</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xxl-4 col-xl-12">
            <div class="card info-card customers-card">
                <div class="card-body">
                    <h5 class="card-title">用户评论 <span>| 总数</span></h5>
                    <div class="d-flex align-items-center">
                        <div class="card-icon rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-chat-left-text"></i>
                        </div>
                        <div class="ps-3">
                            <h6>25,648</h6>
                            <span class="text-danger small pt-1 fw-bold">3%</span> <span class="text-muted small pt-2 ps-1">较上周下降</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">热门景点排名</h5>
                    <div id="popularSpots" style="width: 100%; height: 400px;"></div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">景点评分分布</h5>
                    <div id="ratingDistribution" style="width: 100%; height: 400px;"></div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block active_menu %}home{% endblock %}

{% block echarts %}
<script>
    // 热门景点排名图表
    var chartDom = document.getElementById('popularSpots');
    var myChart = echarts.init(chartDom);
    var option;

    option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {},
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01]
        },
        yAxis: {
            type: 'category',
            data: ['故宫博物院', '西湖', '黄山', '长城', '张家界', '九寨沟', '鼓浪屿', '乐山大佛', '兵马俑', '三亚湾']
        },
        series: [
            {
                name: '月访问量',
                type: 'bar',
                data: [18203, 16489, 15034, 14970, 13491, 12345, 10987, 9876, 8765, 7654]
            },
            {
                name: '好评率',
                type: 'bar',
                data: [92.1, 91.4, 90.7, 89.5, 88.9, 87.6, 86.4, 85.2, 84.1, 83.5]
            }
        ]
    };

    option && myChart.setOption(option);

    // 景点评分分布图表
    var chartDom2 = document.getElementById('ratingDistribution');
    var myChart2 = echarts.init(chartDom2);
    var option2;

    option2 = {
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: '5%',
            left: 'center'
        },
        series: [
            {
                name: '评分分布',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: true,
                    formatter: '{b}: {d}%\n({c})'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 22,
                        fontWeight: 'bold'
                    }
                },
                data: [
                    { value: 1048, name: '5星' },
                    { value: 735, name: '4星' },
                    { value: 580, name: '3星' },
                    { value: 300, name: '2星' },
                    { value: 120, name: '1星' }
                ]
            }
        ]
    };

    option2 && myChart2.setOption(option2);
</script>
{% endblock %}