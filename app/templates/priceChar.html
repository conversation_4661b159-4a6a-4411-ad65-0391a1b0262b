{% extends 'base.html' %}

{% block title %}
    价格和月销量分析
{% endblock %}

{% block sidebar %}
    <aside id="sidebar" class="sidebar">

        <ul class="sidebar-nav" id="sidebar-nav">

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'home' %}">
                    <i class="bi bi-grid"></i>
                    <span>首页</span>
                </a>
            </li>
            <li class="nav-heading">个人信息</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'changeSelfInfo' %}">
                    <i class="bi bi-menu-button-wide"></i><span>修改信息</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'changePassword' %}">
                    <i class="bi bi-journal-text"></i><span>修改密码</span>
                </a>
            </li>

            <li class="nav-heading">数据表格</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'tableData' %}">
                    <i class="bi bi-layout-text-window-reverse"></i><span>数据操作</span>
                </a>
            </li>


            <li class="nav-heading">数据可视化</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'cityChar' %}">
                    <i class="bi bi-bar-chart"></i><span>城市和景点等级分析</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'rateChar' %}">
                    <i class="bi bi-gem"></i><span>评分情况分析</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link " href="{% url 'priceChar' %}">
                    <i class="bi bi-graph-up"></i><span>价格和月销量分析</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'commentsChar' %}">
                    <i class="bi bi-easel-fill"></i><span>评论分析</span>
                </a>
            </li>


            <li class="nav-heading">推荐算法</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'recommendation' %}">
                    <i class="bi bi-hurricane"></i><span>猜你喜欢</span>
                </a>
            </li>

            <li class="nav-heading">词云图</li>
            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'detailIntroCloud' %}">
                    <i class="bi bi-image"></i><span>详情简介词云图</span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link collapsed" href="{% url 'commentContentCloud' %}">
                    <i class="bi bi-image-fill"></i><span>评论内容词云图</span>
                </a>
            </li>


        </ul>

    </aside><!-- End Sidebar-->
{% endblock %}


{% block content %}
    <div class="pagetitle" style="display: flex;align-items: center">
        <div style="margin-right: auto">
            <h1>评分情况分析</h1>
            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'changeSelfInfo' %}">数据可视化</a></li>
                    <li class="breadcrumb-item active">价格和月销量分析</li>
                </ol>
            </nav>
        </div>
        <h5>
            {{ nowTime.year }} - {{ nowTime.mon }} - {{ nowTime.day }}
        </h5>
    </div><!-- End Page Title -->

    <section class="section dashboard">

        {#        <div class="row">#}
        {##}
        {##}
        {#            <div class="col-lg-12">#}
        {##}
        {#                <div class="card">#}
        {#                    <div class="card-body">#}
        {#                        <h5 class="card-title">城市选择</h5>#}
        {#                        <form action="{% url 'priceChar' %}" method="post">#}
        {##}
        {#                            <div class="row mb-3">#}
        {#                                <label class="col-sm-2 col-form-label">城市</label>#}
        {##}
        {#                                <div class="col-sm-10">#}
        {#                                    <select class="form-select" name="province" aria-label="Default select example">#}
        {#                                        {% for i in CityList %}#}
        {#                                            <option value="{{ i }}">{{ i }}</option>#}
        {#                                        {% endfor %}#}
        {#                                    </select>#}
        {#                                    <button style="margin-top:10px" class="btn btn-primary">提交</button>#}
        {#                                </div>#}
        {#                            </div>#}
        {#                        </form>#}
        {##}
        {#                    </div>#}
        {#                </div>#}
        {#            </div>#}
        {#        </div>#}


        <div class="row">

            <!-- Right side columns -->
            <div class="col-lg-6">

                <!-- Recent Activity -->
                <div class="card">

                    <div class="card-body">
                        <h5 class="card-title">价格分析</h5>
                        <div id="mains" style="width: 100%;height: 450px"></div>

                    </div>
                </div>


            </div>


            <div class="col-lg-6">

                <!-- Recent Activity -->
                <div class="card">

                    <div class="card-body">
                        <h5 class="card-title">折扣占比分析</h5>
                        <div id="mainsTrhee" style="width: 100%;height: 450px"></div>
                    </div>
                </div>
            </div>

            <!-- Right side columns -->
            <div class="col-lg-12">

                <!-- Recent Activity -->
                <div class="card">

                    <div class="card-body">
                        <h5 class="card-title">景点销量分析</h5>
                        <div id="mainsTwo" style="width: 100%;height: 450px"></div>

                    </div>
                </div>


            </div>

        </div>
    </section>
{% endblock %}

{% block echarts %}
    <script>
        var chartDom = document.getElementById('mains');
        var myChart = echarts.init(chartDom);
        var option;

        var xData = {{ priceechartsData.xData | safe }};
        var yData = {{ priceechartsData.yData | safe }};

        // 寻找相邻区间的正的最大值及其索引
        function findMaxIncrease(data) {
            var maxIncrease = 0;
            var maxIncreaseIndex = -1;
            for (var i = 0; i < data.length - 1; i++) {
                var increase = data[i + 1] - data[i];
                if (increase > 0 && increase > maxIncrease) {
                    maxIncrease = increase;
                    maxIncreaseIndex = i;
                }
            }
            return {value: maxIncrease, index: maxIncreaseIndex};
        }

        // 寻找相邻区间的绝对值最小值及其索引
        function findMinAbsoluteDifference(data) {
            var minAbsoluteDifference = Infinity;
            var minAbsoluteDifferenceIndex = -1;
            for (var i = 0; i < data.length - 1; i++) {
                var absoluteDifference = Math.abs(data[i + 1] - data[i]);
                if (absoluteDifference < minAbsoluteDifference) {
                    minAbsoluteDifference = absoluteDifference;
                    minAbsoluteDifferenceIndex = i;
                }
            }
            return {value: minAbsoluteDifference, index: minAbsoluteDifferenceIndex};
        }

        var maxIncreaseResult = findMaxIncrease(yData);
        var minAbsoluteDifferenceResult = findMinAbsoluteDifference(yData);

        // 寻找除了上升区间和平缓区间以外的所有区间范围

        function findOtherRanges(data, maxIncreaseIndex, minAbsoluteDifferenceIndex) {
            var otherRanges = [];

            // 将上升区间之前的区间添加到 otherRanges
            if (maxIncreaseIndex > 0) {
                otherRanges.push({start: 0, end: maxIncreaseIndex});
            }

            // 将平缓区间之前的区间添加到 otherRanges
            if (minAbsoluteDifferenceIndex > maxIncreaseIndex + 1) {
                otherRanges.push({start: maxIncreaseIndex + 1, end: minAbsoluteDifferenceIndex});
            }

            // 将平缓区间之后的区间添加到 otherRanges
            if (minAbsoluteDifferenceIndex < data.length - 1) {
                otherRanges.push({start: minAbsoluteDifferenceIndex + 1, end: data.length - 1});
            }

            console.log("Other Ranges:", otherRanges); // 输出其他区间范围，检查是否正确

            return otherRanges;
        }

        var otherRanges = findOtherRanges(yData, maxIncreaseResult.index, minAbsoluteDifferenceResult.index);

        option = {
            title: {
                text: '景点价格趋势分析'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                }
            },
            legend: {
                data: ['价格区间个数']
            },
            toolbox: {
                show: true,
                feature: {
                    saveAsImage: {}
                }
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: xData
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    formatter: '{value} 个'
                },
                axisPointer: {
                    snap: true
                }
            },
            visualMap: {
                show: false,
                dimension: 0,
                pieces: [
                    // 设置上升区间及其相邻区间为红色
                    {
                        gt: maxIncreaseResult.index,
                        lte: maxIncreaseResult.index + 1,
                        color: 'red'
                    },
                    // 设置平缓区间及其相邻区间为红色
                    {
                        gt: minAbsoluteDifferenceResult.index,
                        lte: minAbsoluteDifferenceResult.index + 1,
                        color: 'red'
                    },
                    // 设置除了上升区间和平缓区间以外的所有区间为绿色
                    ...otherRanges.map(function (range) {
                        return {
                            gt: range.start,
                            lte: range.end,
                            color: 'green'
                        };
                    })

                ]
            },
            series: [
                {
                    name: '价格区间个数',
                    type: 'line',
                    smooth: true,
                    data: yData,
                    markArea: {
                        itemStyle: {
                            color: 'rgba(255, 173, 177, 0.4)'
                        },
                        data: [
                            [
                                {
                                    name: '起伏趋势较高',
                                    xAxis: xData[maxIncreaseResult.index]
                                },
                                {
                                    xAxis: xData[maxIncreaseResult.index + 1]
                                }
                            ], [
                                {
                                    name: '较为平缓',
                                    xAxis: xData[minAbsoluteDifferenceResult.index]
                                },
                                {
                                    xAxis: xData[minAbsoluteDifferenceResult.index + 1]
                                }
                            ]
                        ]
                    }
                }
            ]
        };

        option && myChart.setOption(option);


    </script>

    <script>
        var chartDom = document.getElementById('mainsTwo');
        var myChart = echarts.init(chartDom);
        var option;
        option = {
            title: {
                text: ''
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {},
            grid: {
                {#left: '3%',#}
                {#right: '4%',#}
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                boundaryGap: [0, 0.01]
            },
            yAxis: {
                type: 'category',
                data: {{ priceechartsData.x1Data | safe }}
            },
            dataZoom: [
                {
                    show: true,
                    yAxisIndex: 0,
                    width: 30,
                    showDataShadow: false,
                    start: 50,
                    end: 100,
                    left: '3%',
                    right: '4%',
                    {#left: '10%' // 调整dataZoom距离左边的位置#}
                }
            ],
            series: [
                {
                    name: '销量个数',
                    type: 'bar',
                    data: {{ priceechartsData.y1Data | safe }},
                    label: {
                        show: true, // 显示数据标签
                        position: 'right' // 标签位置：顶部
                    }
                }
            ]
        };
        option && myChart.setOption(option);
    </script>

    <script>
        var chartDom = document.getElementById('mainsTrhee');
        var myChart = echarts.init(chartDom);
        var option;

        // 假设这是从服务器端渲染模板后得到的数据
        // 您需要根据实际情况替换或从服务器端获取这些数据
        var rawData = {{ priceechartsData.disCountPieData | safe }};

        // 设置一个阈值，比如小于或等于这个值的将被归为“其他”
        var threshold = 40;

        // 处理数据，将数量较少的项合并为“其他”
        var otherValue = 0;
        var processedData = rawData.reduce(function (result, item) {
            if (item.value < threshold) {
                // 如果数量小于阈值，则累加到“其他”类别
                otherValue += item.value;
            } else {
                // 否则保留当前项
                result.push(item);
            }
            return result;
        }, []);

        // 如果“其他”类别的数量大于0，则添加到数据数组中
        if (otherValue > 0) {
            processedData.push({value: otherValue, name: '其他'});
        }

        option = {
            title: {
                text: '',
                left: 'center'
            },
            tooltip: {
                trigger: 'item'
            },
            legend: {
                type: 'scroll',
                orient: 'vertical',
                left: 'left'
            },
            series: [
                {
                    name: '折扣占比个数',
                    type: 'pie',
                    radius: '50%',
                    data: processedData,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ]
        };

        option && myChart.setOption(option);
    </script>
{% endblock %}

