from django.contrib import admin
from app.models import User

# Register your models here.

@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ['id', 'username', 'password', 'sex', 'address', 'textarea', 'createTime']
    list_per_page = 10  # 每页显示10条数据
    ordering = ('-id',)  # 按照id降序排列
    list_display_links = ['id', 'username']  # 设置哪些字段可以点击跳转到编辑页面
    search_fields = ['username']  # 设置搜索字段
    list_filter = ['sex']  # 设置过滤字段