# Generated by Django 3.1.14 on 2025-07-20 05:43

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='id')),
                ('username', models.CharField(default='', max_length=255, verbose_name='用户名')),
                ('password', models.Char<PERSON>ield(default='', max_length=255, verbose_name='密码')),
                ('sex', models.CharField(default='', max_length=255, verbose_name='性别')),
                ('address', models.CharField(default='', max_length=255, verbose_name='地址')),
                ('avatar', models.FileField(default='avatar/default.png', upload_to='avatar', verbose_name='头像')),
                ('textarea', models.CharField(default='这个人很懒，什么都没写...', max_length=255, verbose_name='个人简介')),
                ('createTime', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '前台用户',
                'verbose_name_plural': '前台用户',
            },
        ),
    ]
